import { UserIncident, Prisma } from '../generated/prisma';
import {
  CreateUserIncidentRequest,
  UpdateUserIncidentRequest,
  UserIncidentResponse,
  PaginatedUserIncidentsResponse,
} from './userIncident.dto';

export class UserIncidentMapper {
  /**
   * Maps a CreateUserIncidentRequest DTO to a UserIncident entity
   */
  static toEntity(dto: CreateUserIncidentRequest): Prisma.UserIncidentCreateInput {
    return {
      userId: dto.userId,
      incidentType: dto.incidentType,
      description: dto.description,
      priority: dto.priority || 'medium',
      status: 'pending',
    };
  }

  /**
   * Maps an UpdateUserIncidentRequest DTO to a UserIncident entity update
   */
  static toUpdateEntity(dto: UpdateUserIncidentRequest): Prisma.UserIncidentUpdateInput {
    const updateData: Prisma.UserIncidentUpdateInput = {};

    if (dto.incidentType !== undefined) {
      updateData.incidentType = dto.incidentType;
    }
    if (dto.description !== undefined) {
      updateData.description = dto.description;
    }
    if (dto.status !== undefined) {
      updateData.status = dto.status;
      // Set resolvedAt when status changes to resolved
      if (dto.status === 'resolved' || dto.status === 'closed') {
        updateData.resolvedAt = new Date();
      }
    }
    if (dto.priority !== undefined) {
      updateData.priority = dto.priority;
    }
    if (dto.assignedTo !== undefined) {
      updateData.assignedTo = dto.assignedTo || null;
    }

    return updateData;
  }

  /**
   * Maps a UserIncident entity to a UserIncidentResponse DTO
   */
  static toResponse(entity: UserIncident): UserIncidentResponse {
    return {
      id: entity.id,
      userId: entity.userId,
      incidentType: entity.incidentType,
      description: entity.description,
      status: entity.status as 'pending' | 'in_progress' | 'resolved' | 'closed',
      priority: entity.priority as 'low' | 'medium' | 'high' | 'critical',
      assignedTo: entity.assignedTo || undefined,
      metadata: entity.metadata as Record<string, unknown> | undefined,
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
      resolvedAt: entity.resolvedAt?.toISOString(),
    };
  }

  /**
   * Maps an array of UserIncident entities to UserIncidentResponse DTOs
   */
  static toResponseArray(entities: UserIncident[]): UserIncidentResponse[] {
    return entities.map(entity => this.toResponse(entity));
  }

  /**
   * Maps UserIncident entities with pagination info to PaginatedUserIncidentsResponse
   */
  static toPaginatedResponse(
    entities: UserIncident[],
    page: number,
    limit: number,
    total: number
  ): PaginatedUserIncidentsResponse {
    return {
      incidents: this.toResponseArray(entities),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}
