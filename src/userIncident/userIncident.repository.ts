import { UserIncident, IUserIncident } from './userIncident.schema';
import { GetUserIncidentsQuery } from './userIncident.dto';
import { FilterQuery } from 'mongoose';

export class UserIncidentRepository {
  /**
   * Creates a new user incident
   */
  async create(incidentData: Partial<IUserIncident>): Promise<IUserIncident> {
    const incident = new UserIncident(incidentData);
    return await incident.save();
  }

  /**
   * Finds a user incident by ID
   */
  async findById(id: string): Promise<IUserIncident | null> {
    return await UserIncident.findById(id);
  }

  /**
   * Finds user incidents with filtering, pagination, and sorting
   */
  async findMany(
    query: GetUserIncidentsQuery
  ): Promise<{ incidents: IUserIncident[]; total: number }> {
    const filter: FilterQuery<IUserIncident> = {};

    // Build filter object
    if (query.userId) {
      filter.userId = query.userId;
    }
    if (query.status) {
      filter.status = query.status;
    }
    if (query.priority) {
      filter.priority = query.priority;
    }
    if (query.incidentType) {
      filter.incidentType = query.incidentType;
    }
    if (query.assignedTo) {
      filter.assignedTo = query.assignedTo;
    }

    // Build sort object
    const sortField = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
    const sort: Record<string, 1 | -1> = { [sortField]: sortOrder };

    // Pagination
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    // Execute queries
    const [incidents, total] = await Promise.all([
      UserIncident.find(filter).sort(sort).skip(skip).limit(limit).exec(),
      UserIncident.countDocuments(filter),
    ]);

    return { incidents, total };
  }

  /**
   * Finds user incidents by user ID
   */
  async findByUserId(userId: string): Promise<IUserIncident[]> {
    return await UserIncident.find({ userId }).sort({ createdAt: -1 }).exec();
  }

  /**
   * Updates a user incident by ID
   */
  async updateById(id: string, updateData: Partial<IUserIncident>): Promise<IUserIncident | null> {
    try {
      return await UserIncident.findByIdAndUpdate(id, updateData, { new: true }).exec();
    } catch (error) {
      return null; // Record not found or other error
    }
  }

  /**
   * Deletes a user incident by ID
   */
  async deleteById(id: string): Promise<IUserIncident | null> {
    try {
      return await UserIncident.findByIdAndDelete(id).exec();
    } catch (error) {
      return null; // Record not found or other error
    }
  }

  /**
   * Finds incidents by status
   */
  async findByStatus(
    status: 'pending' | 'in_progress' | 'resolved' | 'closed'
  ): Promise<IUserIncident[]> {
    return await UserIncident.find({ status }).sort({ createdAt: -1 }).exec();
  }

  /**
   * Finds incidents assigned to a specific user
   */
  async findByAssignedTo(assignedTo: string): Promise<IUserIncident[]> {
    return await UserIncident.find({ assignedTo }).sort({ priority: -1, createdAt: -1 }).exec();
  }

  /**
   * Gets incident statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byPriority: Record<string, number>;
  }> {
    const [total, statusStats, priorityStats] = await Promise.all([
      UserIncident.countDocuments(),
      UserIncident.aggregate([{ $group: { _id: '$status', count: { $sum: 1 } } }]),
      UserIncident.aggregate([{ $group: { _id: '$priority', count: { $sum: 1 } } }]),
    ]);

    const byStatus: Record<string, number> = {};
    statusStats.forEach((stat: { _id: string; count: number }) => {
      byStatus[stat._id] = stat.count;
    });

    const byPriority: Record<string, number> = {};
    priorityStats.forEach((stat: { _id: string; count: number }) => {
      byPriority[stat._id] = stat.count;
    });

    return { total, byStatus, byPriority };
  }
}
