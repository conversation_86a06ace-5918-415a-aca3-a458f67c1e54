import { UserIncidentRepository } from './userIncident.repository';
import { UserIncidentMapper } from './userIncident.mapper';
import { getErrorMessage } from '../common/utils';
import {
  CreateUserIncidentRequest,
  UpdateUserIncidentRequest,
  GetUserIncidentsQuery,
  UserIncidentResponse,
  PaginatedUserIncidentsResponse,
} from './userIncident.dto';

export class UserIncidentService {
  private repository: UserIncidentRepository;

  constructor() {
    this.repository = new UserIncidentRepository();
  }

  /**
   * Creates a new user incident
   */
  async createIncident(request: CreateUserIncidentRequest): Promise<UserIncidentResponse> {
    try {
      const entityData = UserIncidentMapper.toEntity(request);
      const incident = await this.repository.create(entityData);
      return UserIncidentMapper.toResponse(incident);
    } catch (error) {
      throw new Error(`Failed to create incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets a user incident by ID
   */
  async getIncidentById(id: string): Promise<UserIncidentResponse | null> {
    try {
      const incident = await this.repository.findById(id);
      if (!incident) {
        return null;
      }
      return UserIncidentMapper.toResponse(incident);
    } catch (error) {
      throw new Error(`Failed to get incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets user incidents with filtering and pagination
   */
  async getIncidents(query: GetUserIncidentsQuery): Promise<PaginatedUserIncidentsResponse> {
    try {
      const { incidents, total } = await this.repository.findMany(query);
      const page = query.page || 1;
      const limit = query.limit || 10;

      return UserIncidentMapper.toPaginatedResponse(incidents, page, limit, total);
    } catch (error) {
      throw new Error(`Failed to get incidents: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets all incidents for a specific user
   */
  async getIncidentsByUserId(userId: string): Promise<UserIncidentResponse[]> {
    try {
      const incidents = await this.repository.findByUserId(userId);
      return UserIncidentMapper.toResponseArray(incidents);
    } catch (error) {
      throw new Error(`Failed to get user incidents: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Updates a user incident
   */
  async updateIncident(
    id: string,
    request: UpdateUserIncidentRequest
  ): Promise<UserIncidentResponse | null> {
    try {
      const updateData = UserIncidentMapper.toUpdateEntity(request);
      const incident = await this.repository.updateById(id, updateData);
      if (!incident) {
        return null;
      }
      return UserIncidentMapper.toResponse(incident);
    } catch (error) {
      throw new Error(`Failed to update incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Deletes a user incident
   */
  async deleteIncident(id: string): Promise<boolean> {
    try {
      const incident = await this.repository.deleteById(id);
      return incident !== null;
    } catch (error) {
      throw new Error(`Failed to delete incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Assigns an incident to a user
   */
  async assignIncident(id: string, assignedTo: string): Promise<UserIncidentResponse | null> {
    try {
      const updateData = { assignedTo, status: 'in_progress' as const };
      const incident = await this.repository.updateById(id, updateData);
      if (!incident) {
        return null;
      }
      return UserIncidentMapper.toResponse(incident);
    } catch (error) {
      throw new Error(`Failed to assign incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Resolves an incident
   */
  async resolveIncident(id: string): Promise<UserIncidentResponse | null> {
    try {
      const updateData = {
        status: 'resolved' as const,
        resolvedAt: new Date(),
      };
      const incident = await this.repository.updateById(id, updateData);
      if (!incident) {
        return null;
      }
      return UserIncidentMapper.toResponse(incident);
    } catch (error) {
      throw new Error(`Failed to resolve incident: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets incident statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byPriority: Record<string, number>;
  }> {
    try {
      return await this.repository.getStatistics();
    } catch (error) {
      throw new Error(`Failed to get statistics: ${getErrorMessage(error)}`);
    }
  }
}
