import Joi from '@hapi/joi';
import { InputJsonValue } from 'generated/prisma/runtime/library';

// Request DTOs
export interface CreateCrosscoreRequest {
  userId: string;
  verificationType: string;
  requestData: InputJsonValue;
}

export interface UpdateCrosscoreRequest {
  status?: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  responseData?: InputJsonValue;
  experianRequestId?: string;
  experianStatus?: string;
  errorMessage?: string;
  webhookReceived?: boolean;
  webhookData?: InputJsonValue;
}

export interface GetCrosscoreQuery {
  userId?: string;
  status?: string;
  verificationType?: string;
  experianRequestId?: string;
  webhookReceived?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  dateFrom?: string;
  dateTo?: string;
}

// Response DTOs
export interface CrosscoreResponse {
  id: string;
  userId: string;
  requestId: string;
  verificationType: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  requestData: Record<string, unknown>;
  responseData?: Record<string, unknown>;
  experianRequestId?: string;
  experianStatus?: string;
  errorMessage?: string;
  webhookReceived: boolean;
  webhookData?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface PaginatedCrosscoreResponse {
  requests: CrosscoreResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CrosscoreWebhookPayload {
  requestId: string;
  experianRequestId: string;
  status: string;
  data: InputJsonValue;
  timestamp: string;
}

// Validation schemas
export const createCrosscoreSchema = Joi.object({
  userId: Joi.string().required().min(1).max(100),
  verificationType: Joi.string()
    .required()
    .valid(
      'identity_verification',
      'document_verification',
      'address_verification',
      'credit_check',
      'fraud_check',
      'kyc_verification'
    ),
  requestData: Joi.object().required(),
});

export const updateCrosscoreSchema = Joi.object({
  status: Joi.string()
    .optional()
    .valid('pending', 'in_progress', 'completed', 'failed', 'cancelled'),
  responseData: Joi.object().optional(),
  experianRequestId: Joi.string().optional().max(100),
  experianStatus: Joi.string().optional().max(50),
  errorMessage: Joi.string().optional().max(1000),
  webhookReceived: Joi.boolean().optional(),
  webhookData: Joi.object().optional(),
});

export const getCrosscoreQuerySchema = Joi.object({
  userId: Joi.string().optional().min(1).max(100),
  status: Joi.string()
    .optional()
    .valid('pending', 'in_progress', 'completed', 'failed', 'cancelled'),
  verificationType: Joi.string()
    .optional()
    .valid(
      'identity_verification',
      'document_verification',
      'address_verification',
      'credit_check',
      'fraud_check',
      'kyc_verification'
    ),
  experianRequestId: Joi.string().optional().max(100),
  webhookReceived: Joi.boolean().optional(),
  page: Joi.number().optional().min(1).default(1),
  limit: Joi.number().optional().min(1).max(100).default(10),
  sortBy: Joi.string()
    .optional()
    .valid('createdAt', 'updatedAt', 'status', 'verificationType')
    .default('createdAt'),
  sortOrder: Joi.string().optional().valid('asc', 'desc').default('desc'),
  dateFrom: Joi.date().iso().optional(),
  dateTo: Joi.date().iso().optional(),
});

export const crosscoreWebhookSchema = Joi.object({
  requestId: Joi.string().required(),
  experianRequestId: Joi.string().required(),
  status: Joi.string().required(),
  data: Joi.object().required(),
  timestamp: Joi.string().isoDate().required(),
});
