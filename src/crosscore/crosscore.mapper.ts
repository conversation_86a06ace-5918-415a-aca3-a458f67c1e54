import { v4 as uuidv4 } from 'uuid';
import { CrosscoreRequest, Prisma } from '../generated/prisma';
import {
  CreateCrosscoreRequest,
  UpdateCrosscoreRequest,
  CrosscoreResponse,
  PaginatedCrosscoreResponse,
} from './crosscore.dto';
import { InputJsonValue } from 'generated/prisma/runtime/library';

export class CrosscoreMapper {
  /**
   * Maps a CreateCrosscoreRequest DTO to a Crosscore entity
   */
  static toEntity(dto: CreateCrosscoreRequest): Prisma.CrosscoreRequestCreateInput {
    return {
      userId: dto.userId,
      requestId: uuidv4(),
      verificationType: dto.verificationType,
      requestData: dto.requestData,
      status: 'pending',
      webhookReceived: false,
    };
  }

  /**
   * Maps an UpdateCrosscoreRequest DTO to a Crosscore entity update
   */
  static toUpdateEntity(dto: UpdateCrosscoreRequest): Prisma.CrosscoreRequestUpdateInput {
    const updateData: Prisma.CrosscoreRequestUpdateInput = {};

    if (dto.status !== undefined) {
      updateData.status = dto.status;
      // Set completedAt when status changes to completed, failed, or cancelled
      if (['completed', 'failed', 'cancelled'].includes(dto.status)) {
        updateData.completedAt = new Date();
      }
    }
    if (dto.responseData !== undefined) {
      updateData.responseData = dto.responseData;
    }
    if (dto.experianRequestId !== undefined) {
      updateData.experianRequestId = dto.experianRequestId;
    }
    if (dto.experianStatus !== undefined) {
      updateData.experianStatus = dto.experianStatus;
    }
    if (dto.errorMessage !== undefined) {
      updateData.errorMessage = dto.errorMessage;
    }
    if (dto.webhookReceived !== undefined) {
      updateData.webhookReceived = dto.webhookReceived;
    }
    if (dto.webhookData !== undefined) {
      updateData.webhookData = dto.webhookData;
    }

    return updateData;
  }

  /**
   * Maps a Crosscore entity to a CrosscoreResponse DTO
   */
  static toResponse(entity: CrosscoreRequest): CrosscoreResponse {
    return {
      id: entity.id,
      userId: entity.userId,
      requestId: entity.requestId,
      verificationType: entity.verificationType,
      status: entity.status as 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled',
      requestData: entity.requestData as Record<string, unknown>,
      responseData: entity.responseData as Record<string, unknown> | undefined,
      experianRequestId: entity.experianRequestId || undefined,
      experianStatus: entity.experianStatus || undefined,
      errorMessage: entity.errorMessage || undefined,
      webhookReceived: entity.webhookReceived,
      webhookData: entity.webhookData as Record<string, unknown> | undefined,
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
      completedAt: entity.completedAt?.toISOString(),
    };
  }

  /**
   * Maps an array of Crosscore entities to CrosscoreResponse DTOs
   */
  static toResponseArray(entities: CrosscoreRequest[]): CrosscoreResponse[] {
    return entities.map(entity => this.toResponse(entity));
  }

  /**
   * Maps Crosscore entities with pagination info to PaginatedCrosscoreResponse
   */
  static toPaginatedResponse(
    entities: CrosscoreRequest[],
    page: number,
    limit: number,
    total: number
  ): PaginatedCrosscoreResponse {
    return {
      requests: this.toResponseArray(entities),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Maps webhook data to update entity format
   */
  static webhookToUpdateEntity(webhookData: {
    requestId: string;
    experianRequestId: string;
    status: string;
    data: InputJsonValue;
  }): Prisma.CrosscoreRequestUpdateInput {
    return {
      experianRequestId: webhookData.experianRequestId,
      experianStatus: webhookData.status,
      webhookReceived: true,
      webhookData: webhookData.data,
      responseData: webhookData.data,
      status: this.mapExperianStatusToInternalStatus(webhookData.status),
      completedAt: ['completed', 'failed', 'error'].includes(webhookData.status.toLowerCase())
        ? new Date()
        : undefined,
    };
  }

  /**
   * Maps Experian status to internal status
   */
  private static mapExperianStatusToInternalStatus(experianStatus: string): string {
    const status = experianStatus.toLowerCase();
    switch (status) {
      case 'pending':
      case 'submitted':
        return 'pending';
      case 'processing':
      case 'in_progress':
        return 'in_progress';
      case 'completed':
      case 'success':
        return 'completed';
      case 'failed':
      case 'error':
        return 'failed';
      case 'cancelled':
      case 'canceled':
        return 'cancelled';
      default:
        return 'pending';
    }
  }
}
