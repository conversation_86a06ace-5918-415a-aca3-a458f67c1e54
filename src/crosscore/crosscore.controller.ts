import { Request, ResponseToolkit, ResponseObject } from '@hapi/hapi';
import { CrosscoreService } from './crosscore.service';
import { getErrorMessage } from '../common/utils';
import {
  createCrosscoreSchema,
  updateCrosscoreSchema,
  getCrosscoreQuerySchema,
  crosscoreWebhookSchema,
  CreateCrosscoreRequest,
  UpdateCrosscoreRequest,
  GetCrosscoreQuery,
  CrosscoreWebhookPayload,
} from './crosscore.dto';

export class CrosscoreController {
  private service: CrosscoreService;

  constructor() {
    this.service = new CrosscoreService();
  }

  /**
   * Creates a new crosscore verification request
   */
  async createRequest(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { error, value } = createCrosscoreSchema.validate(request.payload);
      if (error) {
        return h.response({ error: error.details[0].message }).code(400);
      }

      const crosscoreRequest = await this.service.createRequest(value as CreateCrosscoreRequest);
      return h.response(crosscoreRequest).code(201);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets a crosscore request by ID
   */
  async getRequestById(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { id } = request.params;
      const crosscoreRequest = await this.service.getRequestById(id);

      if (!crosscoreRequest) {
        return h.response({ error: 'Request not found' }).code(404);
      }

      return h.response(crosscoreRequest).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets a crosscore request by request ID
   */
  async getRequestByRequestId(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { requestId } = request.params;
      const crosscoreRequest = await this.service.getRequestByRequestId(requestId);

      if (!crosscoreRequest) {
        return h.response({ error: 'Request not found' }).code(404);
      }

      return h.response(crosscoreRequest).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets crosscore requests with filtering and pagination
   */
  async getRequests(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { error, value } = getCrosscoreQuerySchema.validate(request.query);
      if (error) {
        return h.response({ error: error.details[0].message }).code(400);
      }

      const requests = await this.service.getRequests(value as GetCrosscoreQuery);
      return h.response(requests).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets all requests for a specific user
   */
  async getRequestsByUserId(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { userId } = request.params;
      const requests = await this.service.getRequestsByUserId(userId);
      return h.response(requests).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Updates a crosscore request
   */
  async updateRequest(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { id } = request.params;
      const { error, value } = updateCrosscoreSchema.validate(request.payload);
      if (error) {
        return h.response({ error: error.details[0].message }).code(400);
      }

      const crosscoreRequest = await this.service.updateRequest(
        id,
        value as UpdateCrosscoreRequest
      );

      if (!crosscoreRequest) {
        return h.response({ error: 'Request not found' }).code(404);
      }

      return h.response(crosscoreRequest).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Cancels a crosscore request
   */
  async cancelRequest(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { id } = request.params;
      const crosscoreRequest = await this.service.cancelRequest(id);

      if (!crosscoreRequest) {
        return h.response({ error: 'Request not found' }).code(404);
      }

      return h.response(crosscoreRequest).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Processes a webhook from Experian/CrossCore
   */
  async processWebhook(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const { error, value } = crosscoreWebhookSchema.validate(request.payload);
      if (error) {
        return h.response({ error: error.details[0].message }).code(400);
      }

      const result = await this.service.processWebhook(value as CrosscoreWebhookPayload);

      if (!result) {
        return h.response({ error: 'Failed to process webhook' }).code(404);
      }

      return h.response({ message: 'Webhook processed successfully' }).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets requests that are pending webhook responses
   */
  async getPendingWebhooks(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const requests = await this.service.getPendingWebhooks();
      return h.response(requests).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }

  /**
   * Gets crosscore request statistics
   */
  async getStatistics(request: Request, h: ResponseToolkit): Promise<ResponseObject> {
    try {
      const statistics = await this.service.getStatistics();
      return h.response(statistics).code(200);
    } catch (error) {
      return h.response({ error: getErrorMessage(error) }).code(500);
    }
  }
}
