import { CrosscoreRequest, Prisma } from '../generated/prisma';
import { DatabaseConnection } from '../common/utils/database';
import { GetCrosscoreQuery } from './crosscore.dto';

export class CrosscoreRepository {
  private prisma = DatabaseConnection.getInstance().getPrismaClient();

  /**
   * Creates a new crosscore request
   */
  async create(requestData: Prisma.CrosscoreRequestCreateInput): Promise<CrosscoreRequest> {
    return await this.prisma.crosscoreRequest.create({
      data: requestData,
    });
  }

  /**
   * Finds a crosscore request by ID
   */
  async findById(id: string): Promise<CrosscoreRequest | null> {
    return await this.prisma.crosscoreRequest.findUnique({
      where: { id },
    });
  }

  /**
   * Finds a crosscore request by request ID
   */
  async findByRequestId(requestId: string): Promise<CrosscoreRequest | null> {
    return await this.prisma.crosscoreRequest.findUnique({
      where: { requestId },
    });
  }

  /**
   * Finds a crosscore request by Experian request ID
   */
  async findByExperianRequestId(experianRequestId: string): Promise<CrosscoreRequest | null> {
    return await this.prisma.crosscoreRequest.findFirst({
      where: { experianRequestId },
    });
  }

  /**
   * Finds crosscore requests with filtering, pagination, and sorting
   */
  async findMany(
    query: GetCrosscoreQuery
  ): Promise<{ requests: CrosscoreRequest[]; total: number }> {
    const where: Prisma.CrosscoreRequestWhereInput = {};

    // Build filter object
    if (query.userId) {
      where.userId = query.userId;
    }
    if (query.status) {
      where.status = query.status;
    }
    if (query.verificationType) {
      where.verificationType = query.verificationType;
    }
    if (query.experianRequestId) {
      where.experianRequestId = query.experianRequestId;
    }
    if (query.webhookReceived !== undefined) {
      where.webhookReceived = query.webhookReceived;
    }

    // Date range filtering
    if (query.dateFrom || query.dateTo) {
      where.createdAt = {};
      if (query.dateFrom) {
        where.createdAt.gte = new Date(query.dateFrom);
      }
      if (query.dateTo) {
        where.createdAt.lte = new Date(query.dateTo);
      }
    }

    // Build sort object
    const sortField = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';
    const orderBy: Prisma.CrosscoreRequestOrderByWithRelationInput = {
      [sortField]: sortOrder,
    };

    // Pagination
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    // Execute queries
    const [requests, total] = await Promise.all([
      this.prisma.crosscoreRequest.findMany({
        where,
        orderBy,
        skip,
        take: limit,
      }),
      this.prisma.crosscoreRequest.count({ where }),
    ]);

    return { requests, total };
  }

  /**
   * Finds crosscore requests by user ID
   */
  async findByUserId(userId: string): Promise<CrosscoreRequest[]> {
    return await this.prisma.crosscoreRequest.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Updates a crosscore request by ID
   */
  async updateById(
    id: string,
    updateData: Prisma.CrosscoreRequestUpdateInput
  ): Promise<CrosscoreRequest | null> {
    try {
      return await this.prisma.crosscoreRequest.update({
        where: { id },
        data: updateData,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Updates a crosscore request by request ID
   */
  async updateByRequestId(
    requestId: string,
    updateData: Prisma.CrosscoreRequestUpdateInput
  ): Promise<CrosscoreRequest | null> {
    try {
      return await this.prisma.crosscoreRequest.update({
        where: { requestId },
        data: updateData,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Updates a crosscore request by Experian request ID
   */
  async updateByExperianRequestId(
    experianRequestId: string,
    updateData: Prisma.CrosscoreRequestUpdateInput
  ): Promise<CrosscoreRequest | null> {
    const existingRequest = await this.prisma.crosscoreRequest.findFirst({
      where: { experianRequestId },
    });

    if (!existingRequest) {
      return null;
    }

    return await this.prisma.crosscoreRequest.update({
      where: { id: existingRequest.id },
      data: updateData,
    });
  }

  /**
   * Deletes a crosscore request by ID
   */
  async deleteById(id: string): Promise<CrosscoreRequest | null> {
    try {
      return await this.prisma.crosscoreRequest.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Finds requests by status
   */
  async findByStatus(
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
  ): Promise<CrosscoreRequest[]> {
    return await this.prisma.crosscoreRequest.findMany({
      where: { status },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Finds requests that haven't received webhooks yet
   */
  async findPendingWebhooks(): Promise<CrosscoreRequest[]> {
    return await this.prisma.crosscoreRequest.findMany({
      where: {
        webhookReceived: false,
        status: { in: ['pending', 'in_progress'] },
      },
      orderBy: { createdAt: 'asc' },
    });
  }

  /**
   * Gets crosscore request statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byVerificationType: Record<string, number>;
    webhookStats: {
      received: number;
      pending: number;
    };
  }> {
    const [total, statusStats, typeStats, webhookStats] = await Promise.all([
      this.prisma.crosscoreRequest.count(),
      this.prisma.crosscoreRequest.groupBy({
        by: ['status'],
        _count: { status: true },
      }),
      this.prisma.crosscoreRequest.groupBy({
        by: ['verificationType'],
        _count: { verificationType: true },
      }),
      this.prisma.crosscoreRequest.groupBy({
        by: ['webhookReceived'],
        _count: { webhookReceived: true },
      }),
    ]);

    const byStatus: Record<string, number> = {};
    statusStats.forEach((stat: { status: string; _count: { status: number } }) => {
      byStatus[stat.status] = stat._count.status;
    });

    const byVerificationType: Record<string, number> = {};
    typeStats.forEach(
      (stat: { verificationType: string; _count: { verificationType: number } }) => {
        byVerificationType[stat.verificationType] = stat._count.verificationType;
      }
    );

    const webhookStatsObj = {
      received: 0,
      pending: 0,
    };
    webhookStats.forEach(
      (stat: { webhookReceived: boolean; _count: { webhookReceived: number } }) => {
        if (stat.webhookReceived === true) {
          webhookStatsObj.received = stat._count.webhookReceived;
        } else {
          webhookStatsObj.pending = stat._count.webhookReceived;
        }
      }
    );

    return {
      total,
      byStatus,
      byVerificationType,
      webhookStats: webhookStatsObj,
    };
  }
}
