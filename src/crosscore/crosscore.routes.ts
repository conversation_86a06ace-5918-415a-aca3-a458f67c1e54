import { Server } from '@hapi/hapi';
import { CrosscoreController } from './crosscore.controller';
import { API_CONSTANTS } from '../common/constants';

export async function registerCrosscoreRoutes(server: Server): Promise<void> {
  // Initialize controller
  const crosscoreController = new CrosscoreController();

  // CrossCore routes
  server.route([
    {
      method: 'POST',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/crosscore/requests`,
      handler: crosscoreController.createRequest.bind(crosscoreController),
      options: {
        description: 'Create a new CrossCore verification request',
        tags: ['api', 'crosscore'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/crosscore/requests/{id}`,
      handler: crosscoreController.getRequestById.bind(crosscoreController),
      options: {
        description: 'Get CrossCore request by ID',
        tags: ['api', 'crosscore'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/crosscore/requests/request-id/{requestId}`,
      handler: crosscoreController.getRequestByRequestId.bind(crosscoreController),
      options: {
        description: 'Get CrossCore request by request ID',
        tags: ['api', 'crosscore'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/crosscore/requests`,
      handler: crosscoreController.getRequests.bind(crosscoreController),
      options: {
        description: 'Get CrossCore requests with filtering and pagination',
        tags: ['api', 'crosscore'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/users/{userId}/crosscore/requests`,
      handler: crosscoreController.getRequestsByUserId.bind(crosscoreController),
      options: {
        description: 'Get CrossCore requests for a specific user',
        tags: ['api', 'crosscore', 'users'],
      },
    },
    {
      method: 'PUT',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/crosscore/requests/{id}`,
      handler: crosscoreController.updateRequest.bind(crosscoreController),
      options: {
        description: 'Update a CrossCore request',
        tags: ['api', 'crosscore'],
      },
    },
    {
      method: 'POST',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/crosscore/requests/{id}/cancel`,
      handler: crosscoreController.cancelRequest.bind(crosscoreController),
      options: {
        description: 'Cancel a CrossCore request',
        tags: ['api', 'crosscore'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/crosscore/pending-webhooks`,
      handler: crosscoreController.getPendingWebhooks.bind(crosscoreController),
      options: {
        description: 'Get requests pending webhook responses',
        tags: ['api', 'crosscore', 'webhooks'],
      },
    },
    {
      method: 'GET',
      path: `${API_CONSTANTS.BASE_PATH}/${API_CONSTANTS.VERSION}/crosscore/statistics`,
      handler: crosscoreController.getStatistics.bind(crosscoreController),
      options: {
        description: 'Get CrossCore request statistics',
        tags: ['api', 'crosscore', 'statistics'],
      },
    },
  ]);

  console.log('CrossCore routes registered');
}
