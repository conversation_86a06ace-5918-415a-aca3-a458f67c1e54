ARG BASE_IMAGE=public.ecr.aws/docker/library/node:22-alpine

ARG DUMB_INIT_VER=~1.2.5

# Stage 1
# This stage build the application
#
FROM ${BASE_IMAGE} AS build
ARG GITHUB_PKG_TOKEN
ENV GITHUB_PKG_TOKEN ${GITHUB_PKG_TOKEN}

WORKDIR /usr/src/app
COPY --chown=node:node package*.json ./
COPY --chown=node:node .npmrc_ci ./
RUN --mount=type=secret,mode=0644,id=npmrc,target=/usr/src/app/.npmrc npm --userconfig=.npmrc_ci ci
# This COPY is intentionally here and allow for efficient Docker layer management
COPY --chown=node:node . .
RUN --mount=type=secret,mode=0644,id=npmrc,target=/usr/src/app/.npmrc npx prisma generate && npm run build && \
  npm prune --production


# Stage 2
# This bustage extract build from stage 1 to produce a lean final image
#
FROM ${BASE_IMAGE}
ARG API_VERSION
ARG DUMB_INIT_VER
ENV API_VERSION=${API_VERSION}
RUN apk --no-cache add dumb-init=$DUMB_INIT_VER
WORKDIR /home/<USER>
# Copy whichever files you need in the running container
COPY --chown=node:node --from=build /usr/src/app/package.json ./package.json
COPY --chown=node:node --from=build /usr/src/app/node_modules ./node_modules
COPY --chown=node:node --from=build /usr/src/app/dist ./dist
COPY --chown=node:node --from=build /usr/src/app/prisma ./prisma
COPY --chown=node:node --from=build /usr/src/app/src/generated ./generated

USER node
CMD [ "npm", "run", "start" ]
